<?php

/**
 * Mail configuration options.
 *
 * Changes to these config files are not supported by BookStack and may break upon updates.
 * Configuration should be altered via the `.env` file or environment variables.
 * Do not edit this file unless you're happy to maintain any changes yourself.
 */

// Configured mail encryption method.
// STARTTLS should still be attempted, but tls/ssl forces TLS usage.
$mailEncryption = env('MAIL_ENCRYPTION', null);
$mailPort = intval(env('MAIL_PORT', 587));

return [

    // Mail driver to use.
    // From Laravel 7+ this is MAIL_MAILER in laravel.
    // Kept as MAIL_DRIVER in BookStack to prevent breaking change.
    // Options: smtp, sendmail, log, array
    'default' => env('MAIL_DRIVER', 'smtp'),

    // Global "From" address & name
    'from' => [
        'address' => env('MAIL_FROM', '<EMAIL>'),
        'name'    => env('MAIL_FROM_NAME', 'BookStack'),
    ],

    // Mailer Configurations
    // Available mailing methods and their settings.
    'mailers' => [
        'smtp' => [
            'transport' => 'smtp',
            'scheme' => null,
            'host' => env('MAIL_HOST', 'smtp.mailgun.org'),
            'port' => $mailPort,
            'username' => env('MAIL_USERNAME'),
            'password' => env('MAIL_PASSWORD'),
            'verify_peer' => env('MAIL_VERIFY_SSL', true),
            'timeout' => null,
            'local_domain' => null,
            'require_tls' => ($mailEncryption === 'tls' || $mailEncryption === 'ssl' || $mailPort === 465),
        ],

        'sendmail' => [
            'transport' => 'sendmail',
            'path' => env('MAIL_SENDMAIL_COMMAND', '/usr/sbin/sendmail -bs'),
        ],

        'log' => [
            'transport' => 'log',
            'channel' => env('MAIL_LOG_CHANNEL'),
        ],

        'array' => [
            'transport' => 'array',
        ],

        'failover' => [
            'transport' => 'failover',
            'mailers' => [
                'smtp',
                'log',
            ],
        ],
    ],
];
