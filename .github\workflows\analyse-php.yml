name: analyse-php

on:
  push:
    paths:
      - '**.php'
  pull_request:
    paths:
      - '**.php'

jobs:
  build:
    if: ${{ github.ref != 'refs/heads/l10n_development' }}
    runs-on: ubuntu-24.04
    steps:
    - uses: actions/checkout@v4

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: 8.3
        extensions: gd, mbstring, json, curl, xml, mysql, ldap

    - name: Get Composer Cache Directory
      id: composer-cache
      run: |
        echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

    - name: Cache composer packages
      uses: actions/cache@v4
      with:
        path: ${{ steps.composer-cache.outputs.dir }}
        key: ${{ runner.os }}-composer-8.3
        restore-keys: ${{ runner.os }}-composer-

    - name: Install composer dependencies
      run: composer install --prefer-dist --no-interaction --ansi

    - name: Run static analysis check
      run: composer check-static
