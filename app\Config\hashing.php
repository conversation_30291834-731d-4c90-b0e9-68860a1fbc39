<?php

/**
 * Hashing configuration options.
 *
 * Changes to these config files are not supported by BookStack and may break upon updates.
 * Configuration should be altered via the `.env` file or environment variables.
 * Do not edit this file unless you're happy to maintain any changes yourself.
 */

return [

    // Default Hash Driver
    // This option controls the default hash driver that will be used to hash
    // passwords for your application. By default, the bcrypt algorithm is used.
    // Supported: "bcrypt", "argon", "argon2id"
    'driver' => 'bcrypt',

    // Bcrypt Options
    // Here you may specify the configuration options that should be used when
    // passwords are hashed using the Bcrypt algorithm. This will allow you
    // to control the amount of time it takes to hash the given password.
    'bcrypt' => [
        'rounds' => env('BCRYPT_ROUNDS', 12),
        'verify' => true,
    ],

    // Argon Options
    // Here you may specify the configuration options that should be used when
    // passwords are hashed using the Argon algorithm. These will allow you
    // to control the amount of time it takes to hash the given password.
    'argon' => [
        'memory'  => 1024,
        'threads' => 2,
        'time'    => 2,
    ],

];
