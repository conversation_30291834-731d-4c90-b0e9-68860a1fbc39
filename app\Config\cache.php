<?php

use Illuminate\Support\Str;

/**
 * Caching configuration options.
 *
 * Changes to these config files are not supported by BookStack and may break upon updates.
 * Configuration should be altered via the `.env` file or environment variables.
 * Do not edit this file unless you're happy to maintain any changes yourself.
 */

// MEMCACHED - Split out configuration into an array
if (env('CACHE_DRIVER') === 'memcached') {
    $memcachedServerKeys = ['host', 'port', 'weight'];
    $memcachedServers = explode(',', trim(env('MEMCACHED_SERVERS', '127.0.0.1:11211:100'), ','));
    foreach ($memcachedServers as $index => $memcachedServer) {
        $memcachedServerDetails = explode(':', $memcachedServer);
        if (count($memcachedServerDetails) < 2) {
            $memcachedServerDetails[] = '11211';
        }
        if (count($memcachedServerDetails) < 3) {
            $memcachedServerDetails[] = '100';
        }
        $memcachedServers[$index] = array_combine($memcachedServerKeys, $memcachedServerDetails);
    }
}

return [

    // Default cache store to use
    // Can be overridden at cache call-time
    'default' => env('CACHE_DRIVER', 'file'),

    // Available caches stores
    'stores' => [

        'array' => [
            'driver'    => 'array',
            'serialize' => false,
        ],

        'database' => [
            'driver'          => 'database',
            'table'           => 'cache',
            'connection'      => null,
            'lock_connection' => null,
            'lock_table'      => null,
        ],

        'file' => [
            'driver' => 'file',
            'path'   => storage_path('framework/cache'),
            'lock_path' => storage_path('framework/cache'),
        ],

        'memcached' => [
            'driver'        => 'memcached',
            'options'       => [
                // Memcached::OPT_CONNECT_TIMEOUT => 2000,
            ],
            'servers' => $memcachedServers ?? [],
        ],

        'redis' => [
            'driver'          => 'redis',
            'connection'      => 'default',
            'lock_connection' => 'default',
        ],

        'octane' => [
            'driver' => 'octane',
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Key Prefix
    |--------------------------------------------------------------------------
    |
    | When utilizing a RAM based store such as APC or Memcached, there might
    | be other applications utilizing the same cache. So, we'll specify a
    | value to get prefixed to all our keys so we can avoid collisions.
    |
    */

    'prefix' => env('CACHE_PREFIX', Str::slug(env('APP_NAME', 'laravel'), '_') . '_cache_'),

];
