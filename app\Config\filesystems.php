<?php

/**
 * Filesystem configuration options.
 *
 * Changes to these config files are not supported by BookStack and may break upon updates.
 * Configuration should be altered via the `.env` file or environment variables.
 * Do not edit this file unless you're happy to maintain any changes yourself.
 */

return [

    // Default Filesystem Disk
    // Options: local, local_secure, s3
    'default' => env('STORAGE_TYPE', 'local'),

    // Filesystem to use specifically for image uploads.
    'images' => env('STORAGE_IMAGE_TYPE', env('STORAGE_TYPE', 'local')),

    // Filesystem to use specifically for file attachments.
    'attachments' => env('STORAGE_ATTACHMENT_TYPE', env('STORAGE_TYPE', 'local')),

    // Storage URL
    // This is the url to where the storage is located for when using an external
    // file storage service, such as s3, to store publicly accessible assets.
    'url' => env('STORAGE_URL', false),

    // Available filesystem disks
    // Only local, local_secure & s3 are supported by BookStack
    'disks' => [

        'local' => [
            'driver'     => 'local',
            'root'       => public_path(),
            'serve'      => false,
            'throw'      => true,
            'directory_visibility' => 'public',
        ],

        'local_secure_attachments' => [
            'driver' => 'local',
            'root'   => storage_path('uploads/files/'),
            'serve'  => false,
            'throw'  => true,
        ],

        'local_secure_images' => [
            'driver'     => 'local',
            'root'       => storage_path('uploads/images/'),
            'serve'      => false,
            'throw'      => true,
        ],

        's3' => [
            'driver'                  => 's3',
            'key'                     => env('STORAGE_S3_KEY', 'your-key'),
            'secret'                  => env('STORAGE_S3_SECRET', 'your-secret'),
            'region'                  => env('STORAGE_S3_REGION', 'your-region'),
            'bucket'                  => env('STORAGE_S3_BUCKET', 'your-bucket'),
            'endpoint'                => env('STORAGE_S3_ENDPOINT', null),
            'use_path_style_endpoint' => env('STORAGE_S3_ENDPOINT', null) !== null,
            'throw'                   => true,
            'stream_reads'            => false,
        ],

    ],

    // Symbolic Links
    // Here you may configure the symbolic links that will be created when the
    // `storage:link` Artisan command is executed. The array keys should be
    // the locations of the links and the values should be their targets.
    'links' => [
        public_path('storage') => storage_path('app/public'),
    ],

];
