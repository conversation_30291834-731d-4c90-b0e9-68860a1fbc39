name: lint-php

on:
  push:
    paths:
      - '**.php'
  pull_request:
    paths:
      - '**.php'

jobs:
  build:
    if: ${{ github.ref != 'refs/heads/l10n_development' }}
    runs-on: ubuntu-24.04
    steps:
    - uses: actions/checkout@v4

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: 8.3
        tools: phpcs

    - name: Run formatting check
      run: composer lint
