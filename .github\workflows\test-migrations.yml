name: test-migrations

on:
  push:
    paths:
      - '**.php'
      - 'composer.*'
  pull_request:
    paths:
      - '**.php'
      - 'composer.*'

jobs:
  build:
    if: ${{ github.ref != 'refs/heads/l10n_development' }}
    runs-on: ubuntu-24.04
    strategy:
      matrix:
        php: ['8.2', '8.3', '8.4']
    steps:
      - uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          extensions: gd, mbstring, json, curl, xml, mysql, ldap

      - name: Get Composer Cache Directory
        id: composer-cache
        run: |
          echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache composer packages
        uses: actions/cache@v4
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ matrix.php }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Start MySQL
        run: |
          sudo systemctl start mysql

      - name: Create database & user
        run: |
          mysql -uroot -proot -e 'CREATE DATABASE IF NOT EXISTS `bookstack-test`;'
          mysql -uroot -proot -e "CREATE USER 'bookstack-test'@'localhost' IDENTIFIED WITH mysql_native_password BY 'bookstack-test';"
          mysql -uroot -proot -e "GRANT ALL ON \`bookstack-test\`.* TO 'bookstack-test'@'localhost';"
          mysql -uroot -proot -e 'FLUSH PRIVILEGES;'

      - name: Install composer dependencies
        run: composer install --prefer-dist --no-interaction --ansi

      - name: Start migration test
        run: |
          php${{ matrix.php }} artisan migrate --force -n --database=mysql_testing

      - name: Start migration:rollback test
        run: |
          php${{ matrix.php }} artisan migrate:rollback --force -n --database=mysql_testing

      - name: Start migration rerun test
        run: |
          php${{ matrix.php }} artisan migrate --force -n --database=mysql_testing
