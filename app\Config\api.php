<?php

/**
 * API configuration options.
 *
 * Changes to these config files are not supported by BookStack and may break upon updates.
 * Configuration should be altered via the `.env` file or environment variables.
 * Do not edit this file unless you're happy to maintain any changes yourself.
 */

return [

    // The default number of items that are returned in listing API requests.
    // This count can often be overridden, up the the max option, per-request via request options.
    'default_item_count' => env('API_DEFAULT_ITEM_COUNT', 100),

    // The maximum number of items that can be returned in a listing API request.
    'max_item_count' => env('API_MAX_ITEM_COUNT', 500),

    // The number of API requests that can be made per minute by a single user.
    'requests_per_minute' => env('API_REQUESTS_PER_MIN', 180),

];
