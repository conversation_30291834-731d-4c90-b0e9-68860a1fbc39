name: Feature Request
description: Request a new feature or idea to be added to BookStack
labels: [":hammer: Feature Request"]
body:
  - type: textarea
    id: description
    attributes:
      label: Describe the feature you'd like
      description: Provide a clear description of the feature you'd like implemented in BookStack
    validations:
      required: true
  - type: textarea
    id: benefits
    attributes:
      label: Describe the benefits this would bring to existing BookStack users
      description: |
        Explain the measurable benefits this feature would achieve for existing BookStack users.
        These benefits should details outcomes in terms of what this request solves/achieves, and should not be specific to implementation.
        This helps us understand the core desired goal so that a variety of potential implementations could be explored.
        This field is important. Lack if input here may lead to early issue closure.
    validations:
      required: true
  - type: textarea
    id: already_achieved
    attributes:
      label: Can the goal of this request already be achieved via other means?
      description: |
        Yes/No. If yes, please describe how the requested approach fits in with the existing method.
    validations:
      required: true
  - type: checkboxes
    id: confirm-search
    attributes:
      label: Have you searched for an existing open/closed issue?
      description: |
        To help us keep these issues under control, please ensure you have first [searched our issue list](https://github.com/BookStackApp/BookStack/issues?q=is%3Aissue) for any existing issues that cover the fundamental benefit/goal of your request.
      options:
        - label: I have searched for existing issues and none cover my fundamental request
          required: true
  - type: dropdown
    id: existing_usage
    attributes:
      label: How long have you been using BookStack?
      options:
        - Not using yet, just scoping
        - Under 3 months
        - 3 months to 1 year
        - 1 to 5 years
        - Over 5 years
    validations:
      required: true
  - type: textarea
    id: context
    attributes:
      label: Additional context
      description: Add any other context or screenshots about the feature request here.
    validations:
      required: false
